#pragma once

#ifdef PLCCLIENT_API_EXPORT
#define PLCCLIENT_API __declspec(dllexport)
#else
#define PLCCLIENT_API
#endif

#include <stdint.h>

extern "C" {

    /**
     * 配置
     */
    struct PlcClientConfigure {

        /** Modbus 连接 */
        const struct ModbusChannel {
            /** IP地址 */
            const char *ip;
            /** 端口号 */
            uint16_t port;
        } *modbusChannels;
        size_t modbusChannelsSize;

        /** 系统 */
        struct System {
            /** Modbus 连接 */
            size_t modbusChannelIndex;

            /** 系统A 基地址 */
            uint16_t systemAAddress;
            /** 系统B 基地址 */
            uint16_t systemBAddress;

            /** 产品编号 */
            const char *productCode;
        } system;

        /** 激光测距工位 */
        const struct WorkstationLaserRanging {
            /** Modbus 连接 */
            size_t modbusChannelIndex;

            /** 工位控制A 基地址 */
            uint16_t workstationControlAAddress;
            /** 工位控制B 基地址 */
            uint16_t workstationControlBAddress;
            /** 状态A 基地址 */
            uint16_t statusAAddress;
            /** 点位B 基地址 */
            uint16_t positionBAddress;

            /** 水平点位数量 */
            uint16_t horizontalPositionSize;

            /** 激光测距的基准值 */
            float referenceValue;
            /** 激光测距的下限值 */
            float lowerLimitValue;
            /** 激光测距的上限值 */
            float upperLimitValue;

            /** 传感器数量 */
            enum SensorSize {
                /** 单传感器 */
                SENSOR_SIZE_ONE = 1,
                /** 双传感器 */
                SENSOR_SIZE_TWO = 2,
            } sensorSize;
        } *workstationLaserRangings;
        /** 激光测距工位数量 */
        size_t workstationLaserRangingsSize;

        /** 2D工位 */
        const struct Workstation2D {
            /** Modbus 连接 */
            size_t modbusChannelIndex;

            /** 工位控制A 基地址 */
            uint16_t workstationControlAAddress;
            /** 工位控制B 基地址 */
            uint16_t workstationControlBAddress;
            /** 点位组B 基地址 */
            uint16_t positionGroupBAddress;

            /** 水平点位数量 */
            uint16_t horizontalPositionSize;

            /** 点位组 */
            const struct PositionGroup {
                /** 光源选择 */
                uint16_t lightMask;
                /** 光源坐标 */
                float lightCoordinates[16];
                /** 点位 */
                const struct Position {
                    /** 保持时长(ms) */
                    uint16_t holdDuration;
                    /** 相机选择 */
                    uint16_t cameraMask;
                    /** 相机坐标 */
                    float cameraCoordinates[16];
                    /** 相机通道类型 */
                    enum CameraChannelType {
                        /** 不属于任何通道 */
                        CAMERA_CHANNEL_TYPE_NONE = 0,
                        /** 通道1 */
                        CAMERA_CHANNEL_TYPE_ONE = 1,
                        /** 通道2 */
                        CAMERA_CHANNEL_TYPE_TWO = 2,
                    } cameraChannelTypes[16];
                } *positions;
                /** 点位数量 */
                size_t positionsSize;
            } *positionGroups;
            /** 点位组数量 */
            size_t positionGroupsSize;
        } *workstation2Ds;
        /** 2D工位数量 */
        size_t workstation2DsSize;

        /** 3D工位 */
        const struct Workstation3D {
            /** Modbus 连接 */
            size_t modbusChannelIndex;

            /** 工位控制A 基地址 */
            uint16_t workstationControlAAddress;
            /** 工位控制B 基地址 */
            uint16_t workstationControlBAddress;
            /** 点位B 基地址 */
            uint16_t positionBAddress;

            /** 水平点位数量 */
            uint16_t horizontalPositionSize;

            /** 点位 */
            const struct Position {
                /** Z轴选择 */
                uint16_t zAxisMask;
                /** Z轴坐标 */
                float zAxisCoordinates[16];
            } *positions;
            /** 点位数量 */
            size_t positionsSize;
        } *workstation3Ds;
        /** 3D工位数量 */
        size_t workstation3DsSize;

        /** 下料工位 */
        const struct WorkstationUnload {
            /** Modbus 连接 */
            size_t modbusChannelIndex;

            /** 工位控制A 基地址 */
            uint16_t workstationControlAAddress;
            /** 工位控制B 基地址 */
            uint16_t workstationControlBAddress;
            /** 分拣结果A 基地址 */
            uint16_t sortResultAAddress;
            /** 判定结果B 基地址 */
            uint16_t resultBAddress;
        } *workstationUnloads;
        /** 下料位数量 */
        size_t workstationUnloadsSize;

        /** 报警 */
        struct Alarm {
            /** Modbus 连接 */
            size_t modbusChannelIndex;

            /** 报警信息A */
            uint16_t alarmInfoAAddress;
        } alarm;
    };

    /**
     * 启动
     * @param plcClientConfigure 配置
     * @return 成功返回 true，失败返回 false
     */
    PLCCLIENT_API bool plcclient_start(const PlcClientConfigure &plcClientConfigure);

    /**
     * 停止
     */
    PLCCLIENT_API void plcclient_stop();

    /**
     * 设置 Modbus 连接状态改变时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setModbusConnectionStateChangedCallback(void (*callback)(size_t modbusChannelIndex,    // Modbus 连接的序号
                                                                                          bool connected)               // 是否已连接
    );

    /** 运行状态 */
    enum PlcClientRunningState {
        /** 运行 */
        PLCCLIENT_RUNNING_STATE_RUNNING = 100,
        /** 停止 */
        PLCCLIENT_RUNNING_STATE_STOPPED = 200,
        /** 暂停 */
        PLCCLIENT_RUNNING_STATE_PAUSED = 300,
    };

    /**
     * 设置运行状态改变时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setRunningStateChangedCallback(void (*callback)(PlcClientRunningState runningState)    // 运行状态
    );

    /**
     * 设置设备状态字改变时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setStateWordChangedCallback(void (*callback)(uint16_t stateWord)                       // 状态字
    );

    /**
     * 设置报警状态改变时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setAlarmStateChangedCallback(void (*callback)(const bool *alarmState)                  // 报警信息
    );

    /**
     * 设置 2D工位 工位开始时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setWorkstationStarted2DCallback(void (*callback)(size_t workstation2DIndex,            // 当前工位在2D工位中的序号
                                                                                  const char *trayCode)                 // 托盘号
    );

    /**
     * 设置 2D工位 点位组开始时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setPositionGroupStarted2DCallback(void (*callback)(size_t workstation2DIndex,          // 当前工位在2D工位中的序号
                                                                                    size_t horizontalPositionIndex,     // 水平点位序号
                                                                                    size_t positionGroupIndex)          // 点位组序号
    );

    /**
     * 设置 2D工位 查询收图结果时的回调函数，有结果时返回
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setQueryPositionGroupResult2DCallback(void (*callback)(size_t workstation2DIndex,      // 当前工位在2D工位中的序号
                                                                                        size_t horizontalPositionIndex, // 水平点位序号
                                                                                        size_t positionGroupIndex,      // 点位组序号
                                                                                        bool &result)                   // 收图结果。true:收图完成 false:收图失败
    );

    /**
     * 设置 2D工位 工位结束时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setWorkstationFinished2DCallback(void(*callback)(size_t workstation2DIndex)            // 当前工位在2D工位中的序号
    );

    /**
     * 设置 3D工位 工位开始时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setWorkstationStarted3DCallback(void (*callback)(size_t workstation3DIndex,            // 当前工位在3D工位中的序号
                                                                                  const char *trayCode)                 // 托盘号
    );

    /**
     * 设置 3D工位 点位开始时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setPositionStarted3DCallback(void (*callback)(size_t workstation3DIndex,               // 当前工位在3D工位中的序号
                                                                               ptrdiff_t &horizontalPositionIndex,      // 水平点位序号。<0表示当前工位结束
                                                                               size_t &positionIndex)                   // 点位序号
    );

    /**
     * 设置 3D工位 点位到达时的回调函数，点位结束时返回
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setPositionArrived3DCallback(void (*callback)(size_t workstation3DIndex,               // 当前工位在3D工位中的序号
                                                                               size_t horizontalPositionIndex,          // 水平点位序号
                                                                               size_t positionIndex)                    // 点位序号
    );

    /**
     * 设置 3D工位 工位结束时的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setWorkstationFinished3DCallback(void (*callback)(size_t workstation3DIndex)           // 当前工位在3D工位中的序号
    );

    /**
     * 设置 下料工位 查询判定结果的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setWorkstationUnloadQueryResultCallback(void (*callback)(size_t workstationUnloadIndex,// 当前工位在下料工位中的序号
                                                                                          const char *trayCode,         // 托盘号
                                                                                          uint8_t *result,              // 判定结果
                                                                                          uint16_t *mould)              // 模号
    );

    /**
     * 分拣结果
     */
    struct SortResult {
        /**
         * 托盘类型 1:OK 2:疑似NG 3:NG
         */
        uint16_t trayType;

        /** 托盘编号（从1开始） */
        uint16_t trayIndex;

        /** 穴位编号（从1开始） */
        uint16_t holeIndex;
    };

    /**
     * 设置 下料工位 上传分拣结果的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setWorkstationUnloadSortResultCallback(void (*callback)(size_t workstationUnloadIndex, // 当前工位在下料工位中的序号
                                                                                         const char *trayCode,          // 托盘号
                                                                                         const SortResult *sortResult,  // 分拣结果
                                                                                         size_t sortResultSize,         // 分拣结果数量
                                                                                         bool &result)                  // 上传结果
    );

    /**
     * 获取版本
     * @return 版本号，格式为 x.x.x
     */
    PLCCLIENT_API const char * plcclient_getVersion();

    enum PlcClientLogType {
        /** 调试信息 */
        PLCCLIENT_LOG_TYPE_DEBUG = 0,

        /** 一般信息 */
        PLCCLIENT_LOG_TYPE_INFO,

        /** 错误 */
        PLCCLIENT_LOG_TYPE_ERROR,
    };

    /**
     * 设置日志的回调函数
     * @param callback 回调函数
     */
    PLCCLIENT_API void plcclient_setLogCallback(void (*callback)(PlcClientLogType logType,  // 日志类型
                                                                 const char *log)           // 日志内容
    );
}
