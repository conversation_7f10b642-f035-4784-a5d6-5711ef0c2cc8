#pragma once

#include "WorkstationExecutor.hpp"
#include "ConfigureUnload.hpp"
#include "ZOffsetManager.hpp"
#include "Util.hpp"

/**
 * 下料工位
 */
class WorkstationUnload final : public WorkstationExecutor {
public:

    class SortResult {
    public:
        /** 料盘类型 */
        std::uint16_t trayType {};
        /** 料盘编号 */
        std::uint16_t trayIndex {};
        /** 穴位编号 */
        std::uint16_t holeIndex {};
    };

    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param zOffsetManager Z偏差值管理器
     * @param configureUnload 工位配置
     * @param disableLaserRanging 停用激光测距
     * @param queryResultCallback 查询判定结果的回调
     * @param sortResultCallback 上传分拣结果的回调
     */
    explicit WorkstationUnload(std::string name,
                    ModbusTcpMaster &modbusTcpMaster,
                    ZOffsetManager &zOffsetManager,
                    const ConfigureUnload &configureUnload,
                    std::atomic<bool> &disableLaserRanging,
                    std::function<std::array<std::uint8_t, 400>(const std::string &trayCode, std::uint16_t mould)> queryResultCallback,
                    std::function<bool(const std::string &trayCode, const std::vector<SortResult> &sortResults)> sortResultCallback)
      : WorkstationExecutor(std::move(name), modbusTcpMaster, configureUnload.workstationControlAAddress, configureUnload.workstationControlBAddress),
        zOffsetManager(zOffsetManager),
        configureUnload(configureUnload),
        disableLaserRanging(disableLaserRanging),
        queryResultCallback(std::move(queryResultCallback)),
        sortResultCallback(std::move(sortResultCallback)) {
        start();
    };
    ~WorkstationUnload() override {
        stop();
    }

protected:

    /**
     * 初始化：初始化工位控制B、判定结果B
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        return WorkstationExecutor::initialize() &&
               modbusTcpMaster.write(configureUnload.resultBAddress,UnloadResultB().toByteVector());
    }

    /**
     * 工位开始
     * @param trayCode 托盘编号
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStart(const std::string &trayCode) override {
        currentTrayCode = trayCode;
        nextStatus = Status::WAIT_RESULT_WRITE;

        try {
            zOffsetManager.deleteTray(currentTrayCode);
            Logger::log(Logger::DEBUG, name + std::format(" 删除测距数据 托盘号:{}", currentTrayCode));
        } catch (const ZOffsetManager::OffsetNotExistsException &e) {
            if (!disableLaserRanging.load()) {
                Logger::log(Logger::ERR, name + " 删除测距数据时 数据不存在: " + e.what());
            }
        }

        return ResponseType::START_WORKSTATION_SUCCESS;
    }

    /**
     * 工位结束
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStop() override {
        if (currentStatus == Status::WAIT_WORKSTATION_FINISH) {
            nextStatus = Status::WAIT_WORKSTATION_START;
            return ResponseType::FINISH_WORKSTATION_SUCCESS;
        }

        return ResponseType::FINISH_WORKSTATION_STATUS_ERROR;
    }

    /**
     * 请求
     * @param request 请求
     * @return 回复
     */
    std::optional<ResponseType> request(const std::uint16_t request) override {
        if (request == static_cast<std::uint16_t>(RequestType::RESULT_WRITE)) {
            Logger::log(Logger::INFO, name + std::format(" 查询判定结果 托盘号:{}", currentTrayCode));

            if (currentStatus != Status::WAIT_RESULT_WRITE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::RESULT_WRITE_STATUS_ERROR;
            }

            if (std::uint16_t mould {}, const auto result = queryResultCallback(currentTrayCode, mould); !modbusTcpMaster.write(configureUnload.resultBAddress, UnloadResultB{result}.toByteVector())) {
                Logger::log(Logger::INFO, name + " 写入判定结果失败");
                return std::nullopt;
            }
            Logger::log(Logger::INFO, name + " 写入判定结果成功");
            nextStatus = Status::WAIT_SORT_RESULT;
            return ResponseType::RESULT_WRITE_SUCCESS;
        }

        if (request == static_cast<std::uint16_t>(RequestType::SORT_RESULT)) {
            Logger::log(Logger::INFO, name + std::format(" 上传分拣结果 托盘号:{}", currentTrayCode));

            if (currentStatus != Status::WAIT_SORT_RESULT) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::SORT_RESULT_STATUS_ERROR;
            }

            std::vector<std::uint8_t> vectorSortResultA(SortResultA::SIZE);
            if (!modbusTcpMaster.read(configureUnload.sortResultAAddress, vectorSortResultA)) {
                Logger::log(Logger::INFO, name + " 读取分拣结果失败");
                return std::nullopt;
            }

            const auto [workpieceSize, workpieceInfos] = SortResultA::fromByteVector(vectorSortResultA);

            if (workpieceSize == 0 || workpieceSize > workpieceInfos.size()) {
                Logger::log(Logger::INFO, name + std::format(" 分拣结果中的工件数量错误:{}", workpieceSize));
                return ResponseType::SORT_RESULT_NOT_CORRECT;
            }

            std::vector<SortResult> sortResults;

            std::ostringstream oss;
            oss << std::endl << "src_hole_index    dest_tray_type    dest_tray_index   dest_hole_index   " << std::endl;

            for (int i=0;i<workpieceSize;i++) {
                sortResults.push_back({
                    workpieceInfos[i].trayType,
                    workpieceInfos[i].trayIndex,
                    workpieceInfos[i].holeIndex
                });
                oss << std::left
                    << std::setw(18) << i+1
                    << std::setw(18) << sortResults.back().trayType
                    << std::setw(18) << sortResults.back().trayIndex
                    << std::setw(18) << sortResults.back().holeIndex << std::endl;
            }

            oss << std::endl << "SNG 和 NG 结果筛选：" << std::endl;
            oss << "src_hole_index    dest_tray_type    dest_tray_index   dest_hole_index   " << std::endl;

            for (std::size_t i = 0;i<sortResults.size();i++) {
                if (sortResults[i].trayType == 2 || sortResults[i].trayType == 3) {
                    oss << std::left
                    << std::setw(18) << i+1
                    << std::setw(18) << sortResults[i].trayType
                    << std::setw(18) << sortResults[i].trayIndex
                    << std::setw(18) << sortResults[i].holeIndex << std::endl;
                }
            }

            Logger::log(Logger::INFO, name + " 分拣结果:" + oss.str());

            if (!sortResultCallback(currentTrayCode, sortResults)) {
                Logger::log(Logger::INFO, name + " 上传分拣结果失败");
                return std::nullopt;
            }
            Logger::log(Logger::INFO, name + " 上传分拣结果成功");
            nextStatus = Status::WAIT_WORKSTATION_FINISH;
            return ResponseType::SORT_RESULT_SUCCESS;
        }

        return std::nullopt;
    }

private:

    /** 偏差值管理器 */
    ZOffsetManager &zOffsetManager;

    /** 配置 */
    ConfigureUnload configureUnload;

    /** 停用激光测距 */
    std::atomic<bool> &disableLaserRanging;

    /** 当前托盘号 */
    std::string currentTrayCode {};

    /** 查询判定结果的回调 */
    const std::function<std::array<std::uint8_t, 400>(const std::string &trayCode, std::uint16_t mould)> queryResultCallback {};

    /** 上传分拣结果的回调 */
    const std::function<bool(const std::string &trayCode, const std::vector<SortResult> &sortResults)> sortResultCallback {};

private:

    /** 分拣结果 A */
    class SortResultA final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 工件数量 */
            static constexpr std::uint16_t WORKPIECE_SIZE = 0;

            /** 工件信息 */
            static constexpr std::uint16_t WORKPIECE_INFOS = 2;

            /** 料盘类型 */
            static constexpr std::uint16_t TRAY_TYPE = 0;

            /** 料盘编号 */
            static constexpr std::uint16_t TRAY_INDEX = 2;

            /** 穴位 */
            static constexpr std::uint16_t HOLE_INDEX = 4;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 2402;

    public:

        /** 工件数量 */
        std::uint16_t workpieceSize {};

        class WorkpieceInfo {
        public:
            /** 料盘类型 */
            std::uint16_t trayType {};

            /** 料盘编号 */
            std::uint16_t trayIndex {};

            /** 穴位编号 */
            std::uint16_t holeIndex {};
        };

        /** 分拣结果 */
        std::array<WorkpieceInfo, 400> workpieceInfos {};

    public:

        /**
         * 转换为字节容器
         * @return 字节容器
         */
        [[nodiscard]] std::vector<std::uint8_t> toByteVector() const {
            std::vector<std::uint8_t> vector;

            Util::appendToByteVector(vector, workpieceSize);

            for (auto [trayType, trayIndex, holeIndex] : workpieceInfos) {
                Util::appendToByteVector(vector, trayType);
                Util::appendToByteVector(vector, trayIndex);
                Util::appendToByteVector(vector, holeIndex);
            }

            return vector;
        }

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return B
         */
        static SortResultA fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("下料位 分拣结果A 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            SortResultA sortResultA {};

            sortResultA.workpieceSize = *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::WORKPIECE_SIZE]);

            for (int i=0;i<sortResultA.workpieceInfos.size();i++) {
                sortResultA.workpieceInfos[i].trayType = *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::WORKPIECE_INFOS + Offset::TRAY_TYPE + i*sizeof(WorkpieceInfo)]);
                sortResultA.workpieceInfos[i].trayIndex = *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::WORKPIECE_INFOS + Offset::TRAY_INDEX + i*sizeof(WorkpieceInfo)]);
                sortResultA.workpieceInfos[i].holeIndex = *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::WORKPIECE_INFOS + Offset::HOLE_INDEX + i*sizeof(WorkpieceInfo)]);
            }

            return sortResultA;
        }
    };

    /** 判定结果 B */
    class UnloadResultB final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 判定结果 */
            static constexpr std::uint16_t RESULT = 0;

            /** 模号 */
            static constexpr std::uint16_t MOULD = 400;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 402;

    public:

        /** 判定结果 */
        std::array<std::uint8_t, 400> result {};

        /** 模号 */
        std::uint16_t mould {};

    public:

        /**
         * 转换为字节容器
         * @return 字节容器
         */
        [[nodiscard]] std::vector<std::uint8_t> toByteVector() const {
            std::vector<std::uint8_t> vector;

            vector.insert(vector.end(), result.begin(), result.end());
            Util::appendToByteVector(vector, mould);

            return vector;
        }

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return B
         */
        static UnloadResultB fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("下料位 判定结果B 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            UnloadResultB unloadResultB {};

            std::memcpy(unloadResultB.result.data(), byteVector.data()+Offset::RESULT, unloadResultB.result.size());
            unloadResultB.mould = *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::MOULD]);

            return unloadResultB;
        }
    };
};